# Directory Structure
```
src/views/yqkeysignal/components/AddFromLibraryDialog.vue
src/views/yqkeysignal/components/DetailForm.vue
src/views/yqkeysignal/components/DetailTable.vue
src/views/yqkeysignal/components/GroupForm.vue
src/views/yqkeysignal/components/GroupPanel.vue
src/views/yqkeysignal/components/GroupSettings.vue
src/views/yqkeysignal/index.vue
```

# Files

## File: src/views/yqkeysignal/components/GroupForm.vue
```vue
<template>
  <el-dialog :title="title" :visible.sync="visible" width="500px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="分组名称" prop="groupName">
        <el-input v-model="form.groupName" placeholder="请输入分组名称" />
      </el-form-item>
      <el-form-item label="分组描述" prop="groupDescription">
        <el-input v-model="form.groupDescription" type="textarea" placeholder="请输入分组描述 (可选)" />
      </el-form-item>
      <el-form-item label="分组颜色" prop="groupColor">
         <el-radio-group v-model="form.groupColor">
            <el-radio-button v-for="color in colors" :key="color" :label="color">
              <span class="color-dot" :style="{ backgroundColor: color }"></span>
            </el-radio-button>
          </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addGroup, updateGroup } from "@/api/yqkeysignal/group";

export default {
  name: "GroupForm",
  data() {
    return {
      visible: false,
      title: '',
      form: {},
      rules: {
        groupName: [
          { required: true, message: "分组名称不能为空", trigger: "blur" }
        ],
        groupColor: [
          { required: true, message: "请选择一个分组颜色", trigger: "change" }
        ]
      },
      // 预设颜色
      colors: ['#F56C6C', '#67C23A', '#409EFF', '#E6A23C', '#909399', '#B87FEA', '#5AC8FA', '#FF5B5A']
    };
  },
  methods: {
    open(mode = 'add', data = null) {
      this.reset();
      if (mode === 'add') {
        this.title = "新建分组";
        this.form.groupColor = this.colors[0]; // 默认选第一个颜色
      } else {
        this.title = "编辑分组";
        this.form = { ...data, id: data.groupId };
      }
      this.visible = true;
    },
    reset() {
      this.form = {
        id: undefined,
        groupName: undefined,
        groupDescription: undefined,
        groupColor: this.colors[0]
      };
      this.resetForm("form");
    },
    cancel() {
      this.visible = false;
      this.reset();
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id) {
            updateGroup(this.form.id, this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.visible = false;
              this.$emit("submit-success");
            });
          } else {
            addGroup(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.visible = false;
              this.$emit("submit-success");
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.color-dot {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  vertical-align: middle;
}
.el-radio-button__inner {
  padding: 8px;
}
</style>
```

## File: src/views/yqkeysignal/components/GroupSettings.vue
```vue
<template>
    <el-dialog title="分组设置" :visible.sync="visible" width="600px" append-to-body>
        <div v-if="form.id">
            <div class="setting-item">
                <div class="setting-title">{{ form.groupName }}</div>
                <div class="setting-desc">{{ form.groupDescription }}</div>
            </div>
            <el-divider></el-divider>

            <el-form ref="form" :model="settings" label-position="top">
                <el-form-item>
                    <el-checkbox v-model="settings.syncCreateFlag" true-label="1"
                        false-label="0">同步设置为定向信源组</el-checkbox>
                    <div class="setting-help-text">启用后，该分组将同步为定向信源组，可在专题中使用。</div>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="settings.monitorFeatureFlag" true-label="1"
                        false-label="0">应用于监测舆情特征</el-checkbox>
                    <div class="setting-help-text">应用后，该分组内的信源将用于舆情特征分析，帮助识别和分析相关舆情趋势。</div>
                </el-form-item>
            </el-form>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">保存设置</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { updateGroupSettings } from "@/api/yqkeysignal/group";

export default {
    name: "GroupSettings",
    data() {
        return {
            visible: false,
            form: {},
            settings: {
                syncCreateFlag: "0",
                monitorFeatureFlag: "0"
            }
        }
    },
    methods: {
        open(data) {
            this.form = {
                ...data,
                id: data.groupId
            };
            this.settings.syncCreateFlag = data.syncCreateFlag || '0';
            this.settings.monitorFeatureFlag = data.monitorFeatureFlag || '0';
            this.visible = true;
        },
        cancel() {
            this.visible = false;
        },
        submitForm() {
            updateGroupSettings(this.form.id, this.settings).then(() => {
                this.$modal.msgSuccess("设置成功");
                this.visible = false;
                this.$emit("submit-success");
            });
        }
    }
}
</script>

<style scoped>
.setting-item {
    margin-bottom: 20px;
}

.setting-title {
    font-size: 16px;
    font-weight: bold;
}

.setting-desc {
    font-size: 14px;
    color: #999;
    margin-top: 5px;
}

.setting-help-text {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
    margin-left: 25px;
    margin-top: -5px;
}
</style>
```

## File: src/views/yqkeysignal/components/DetailForm.vue
```vue
<template>
  <el-dialog title="修改信源信息" :visible.sync="visible" width="600px" append-to-body>
    <el-form ref="form" :model="form" label-width="100px" v-if="form.id">
      <el-form-item label="账号昵称">
        <span>{{ form.accountNickname }}</span>
      </el-form-item>

      <el-form-item label="级别分类" prop="sourceImportance">
        <el-select v-model="form.sourceImportance" placeholder="请选择级别分类" style="width: 100%;">
          <el-option v-for="dict in dict.type.source_importance" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="网民类型" prop="audienceTypes">
        <el-select v-model="form.audienceTypes" multiple placeholder="请选择网民类型 (可多选)" style="width: 100%;">
          <el-option v-for="dict in dict.type.audience_type" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="所属分组" prop="groupIds">
        <el-select v-model="form.groupIds" multiple placeholder="请选择所属分组 (可多选)" style="width: 100%;">
          <el-option v-for="group in groupList" :key="group.id" :label="group.groupName" :value="group.id">
            <span style="float: left">{{ group.groupName }}</span>
            <span class="option-color-dot" :style="{ backgroundColor: group.groupColor }"></span>
          </el-option>
        </el-select>
      </el-form-item>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">保存修改</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateDetail } from "@/api/yqkeysignal/detail";

export default {
  name: "DetailForm",
  dicts: ['audience_type', 'source_importance'],
  data() {
    return {
      visible: false,
      form: {},
      groupList: [],
    };
  },
  methods: {
    open(data, groupList) {
      this.reset();
      this.groupList = groupList;

      // [核心修复] 深拷贝数据，并手动将 detailId 映射到 form.id
      // 这样，后续的 this.form.id 判断和使用都能正常工作
      const formData = JSON.parse(JSON.stringify(data));
      formData.id = data.detailId; // <-- 关键修复！

      // --- 以下为处理下拉框数据的逻辑，保持不变 ---
      if (formData.audienceTypes && typeof formData.audienceTypes === 'string') {
        formData.audienceTypes = formData.audienceTypes.split(',');
      } else if (!formData.audienceTypes) {
        formData.audienceTypes = [];
      }
      // 后端返回的明细对象中没有 groups 数组，所以这个逻辑可能不需要
      // 但保留它以防万一
      formData.groupIds = data.groups ? data.groups.map(g => g.groupId) : (data.groupIds || []);

      this.form = formData;
      this.visible = true;
    },
    reset() {
      this.form = {
        id: undefined,
        accountNickname: undefined,
        sourceImportance: undefined,
        audienceTypes: [],
        groupIds: []
      };
      this.resetForm("form");
    },
    cancel() {
      this.visible = false;
      this.reset();
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const submitData = {
            detailId: this.form.id,
            sourceImportance: this.form.sourceImportance,
            audienceTypes: this.form.audienceTypes.join(','),
            groupIds: this.form.groupIds
          };

          updateDetail(submitData).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.visible = false;
            this.$emit("submit-success");
          });
        }
      });
    }
  }
};
</script>
<style scoped>
.option-color-dot {
  float: right;
  margin-top: 13px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
</style>
```

## File: src/views/yqkeysignal/components/GroupPanel.vue
```vue
<template>
  <div class="group-panel-wrapper">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>分组管理 ({{ total }}个分组)</span>
        <div class="header-buttons">
          <el-button icon="el-icon-plus" circle size="mini" @click="handleAdd"></el-button>
        </div>
      </div>
      <div class="group-search-form">
        <el-form :model="groupQueryParams" ref="groupQueryForm" size="mini" :inline="true" class="group-query-form">
          <el-form-item prop="keyword">
            <el-input v-model="groupQueryParams.keyword" placeholder="分组名称/描述" clearable style="width: 100%;"
              @keyup.enter.native="handleGroupQuery" />
          </el-form-item>
          <el-form-item prop="syncCreateFlag">
            <el-select v-model="groupQueryParams.syncCreateFlag" placeholder="是否定向" clearable style="width: 100%;">
              <el-option v-for="dict in dict.type.sync_create_flag" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="monitorFeatureFlag">
            <el-select v-model="groupQueryParams.monitorFeatureFlag" placeholder="是否应用特征" clearable
              style="width: 100%;">
              <el-option v-for="dict in dict.type.monitor_feature_flag" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleGroupQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetGroupQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="group-list">
        <!-- 全部信源 -->
        <div class="group-item" :class="{ 'active': activeGroupId === null }" @click="handleSelectGroup(null, '全部信源')">
          <div class="group-info">
            <span class="group-name">全部信源</span>
          </div>
          <span class="group-count">{{ counts.total || 0 }}</span>
        </div>

        <!-- 未分组 -->
        <div class="group-item" :class="{ 'active': activeGroupId === 0 }" @click="handleSelectGroup(0, '未分组')">
          <div class="group-info">
            <span class="group-name">未分组</span>
          </div>
          <span class="group-count">{{ counts.ungrouped || 0 }}</span>
        </div>

        <!-- 新增：添加分割线和标题以区分区域 -->
        <el-divider v-if="groupList.length > 0" content-position="left" class="list-divider">已分组</el-divider>

        <!-- 分组列表 -->
        <el-scrollbar class="group-list-scrollbar" v-loading="loading">
          <!-- 确保 v-for 和 :key, :class 的正确绑定 -->
          <div v-for="group in groupList" :key="group.groupId" class="group-item"
            :class="{ 'active': activeGroupId === group.groupId }"
            @click="handleSelectGroup(group.groupId, group.groupName)">
            <div class="group-info">
              <span class="group-color-dot" :style="{ backgroundColor: group.groupColor }" v-if="group.groupColor"></span>
              <div class="name-desc">
                <el-tooltip class="item" effect="dark" :content="group.groupName" placement="top">
                  <div class="group-name">{{ group.groupName }}</div>
                </el-tooltip>
                <el-tooltip v-if="group.groupDescription" class="item" effect="dark" :content="group.groupDescription"
                  placement="top">
                  <div class="group-description">{{ group.groupDescription }}</div>
                </el-tooltip>
              </div>
            </div>
            <div class="group-actions">
              <el-button type="text" icon="el-icon-edit" size="mini" @click.stop="handleEdit(group)"></el-button>
              <el-button type="text" icon="el-icon-setting" size="mini" @click.stop="handleSettings(group)"></el-button>
              <el-button type="text" icon="el-icon-delete" size="mini" @click.stop="handleDelete(group)"></el-button>
            </div>
          </div>
        </el-scrollbar>

      </div>
    </el-card>

    <!-- 分组表单弹窗 -->
    <GroupForm ref="groupForm" @submit-success="handleGroupsChange" />
    <!-- 分组设置弹窗 -->
    <GroupSettings ref="groupSettings" @submit-success="handleGroupsChange" />

  </div>
</template>

<script>
import { listGroup, delGroup } from "@/api/yqkeysignal/group";
import { getDetailCount } from "@/api/yqkeysignal/detail";
import GroupForm from './GroupForm';
import GroupSettings from './GroupSettings';

export default {
  name: "GroupPanel",
  dicts: ['sync_create_flag', 'monitor_feature_flag'],
  components: { GroupForm, GroupSettings },
  props: {},
  data() {
    return {
      loading: false,
      groupList: [],
      total: 0,
      activeGroupId: null, // null for '全部信源', 0 for '未分组'
      counts: {
        total: 0,
        ungrouped: 0
      },
      groupQueryParams: {
        pageNum: 1, // 分页页码
        pageSize: 1000, // 每页数量（设置一个较大值）
        keyword: undefined,
        syncCreateFlag: undefined,
        monitorFeatureFlag: undefined
      }
    };
  },
  created() {
    this.getList(); // 首次加载分组列表
    this.refreshCounts();
  },
  methods: {
    /** 查询分组列表 */
    getList() {
      this.loading = true; // 开始加载
      // 直接调用 API，并传入当前搜索条件
      return listGroup(this.groupQueryParams).then(response => {
        // 对 response.rows 进行 map，验证 groupColor 是否为有效的颜色值
        this.groupList = response.rows.map(item => {
          // 检查 groupColor 是否为有效的颜色值（形如 '#FFFFFF'）
          if (item.groupColor && !/^#[0-9A-Fa-f]{6}$/.test(item.groupColor)) {
            item.groupColor = '#67C23A'; // 设置为默认颜色
          }
          return item;
        });
        this.total = response.total; // 从 API 响应中获取真实的总数
        this.loading = false; // 加载完成
        this.$emit('update:group-list', this.groupList);
      }).catch(() => {
        this.loading = false; // 加载失败
      });
    },
    /** 刷新信源计数 */
    refreshCounts() {
      getDetailCount().then(res => {
        this.counts.total = res.data.totalCount;
        this.counts.ungrouped = res.data.ungroupedCount;
      })
    },
    handleGroupQuery() {
      this.getList().then(() => {
        // 搜索完成后，检查当前 selection 是否仍然有效
        const currentSelectionExists = this.groupList.some(g => g.groupId === this.activeGroupId);

        // 如果 activeGroupId 不是“全部信源”(null)或“未分组”(0)，
        // 并且它在新的 groupList 中已经不存在了
        if (this.activeGroupId !== null && this.activeGroupId !== 0 && !currentSelectionExists) {
          // 那么就将选择重置为“全部信源”，以避免一个无效的“悬空”选择
          this.$modal.msgWarning("原选中分组已被筛除，已自动切换至“全部信源”");
          this.handleSelectGroup(null, '全部信源');
        }
        // 如果之前的选择仍然有效（比如之前选的就是“全部信源”，或者选中的分组仍在筛选结果中），
        // 则什么也不做，保持原样。这样就防止了自动选择第一个结果的BUG。
      });
    },
    resetGroupQuery() {
      this.resetForm("groupQueryForm");
      this.handleGroupQuery();
    },
    /** 选择分组 */
    handleSelectGroup(id, name) {
      if (this.activeGroupId === id) return;
      this.activeGroupId = id;
      this.$emit('group-selected', { id, name });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.groupForm.open('add');
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.$refs.groupForm.open('edit', row);
    },
    /** 设置按钮操作 */
    handleSettings(row) {
      this.$refs.groupSettings.open(row);
    },
    handleGroupsChange() {
      this.getList(); // 1. 刷新自己的分组列表
      this.refreshCounts(); // 2. 刷新“全部”和“未分组”计数
      this.$emit('details-changed'); // 3. 通知父组件（名称保持一致，让父组件刷新信源明细和全局分组列表）
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const groupIds = row.groupId;
      const groupName = row.groupName;
      this.$modal.confirm(`确定要删除分组【${groupName}】吗？删除后，该分组下的信源将变为未分组状态。`).then(() => {
        return delGroup(groupIds);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.handleGroupsChange(); // 调用统一处理函数
        // 如果删除的是当前选中的分组，则切换到“全部信源”
        if (this.activeGroupId === groupIds) {
          this.handleSelectGroup(null, '全部信源');
        }
      }).catch(() => { });
    },
  }
};
</script>

<style lang="scss" scoped>
.group-panel-wrapper .el-card__header {
  padding: 12px 15px;
}

.clearfix {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-list-scrollbar {
  height: calc(100vh - 280px);
  /* 根据实际情况调整 */
  overflow-x: hidden;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 5px;
  font-size: 14px;

  .group-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
    overflow: hidden;
  }

  .group-color-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .name-desc {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .group-name {
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .group-description {
    font-size: 12px;
    color: #999;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .group-count {
    color: #909399;
    margin-left: 10px;
  }

  .group-actions {
    display: none;
  }

  &:hover {
    background-color: #f5f7fa;

    .group-actions {
      display: block;
    }
  }

  &.active {
    background-color: #ecf5ff;
    color: #409eff;
  }
}

.group-search-form {
  padding: 0 10px 10px 10px;
  border-bottom: 1px solid #e6ebf5;

  .el-form-item {
    margin-bottom: 0;
  }
}

.group-query-form {
  .el-form-item {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.list-divider {
  margin: 15px 0 10px 0 !important; // 增加上下边距，确保与内容分开

  .el-divider__text {
    font-size: 13px;
    color: #a9a9a9;
    font-weight: normal;
  }
}
</style>
```

## File: src/views/yqkeysignal/index.vue
```vue
<template>
  <div class="app-container key-signal-container">
    <el-row :gutter="20">
      <!--左侧分组管理-->
      <el-col :span="5" :xs="24">
        <GroupPanel ref="groupPanel" @group-selected="handleGroupSelected" @details-changed="handleDetailsChanged"
          @update:group-list="updateMasterGroupList" />
      </el-col>

      <!--右侧信源列表-->
      <el-col :span="19" :xs="24">
        <DetailTable ref="detailTable" :active-group="activeGroup" :group-list="masterGroupList"
          @details-changed="handleDetailsChanged" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import GroupPanel from './components/GroupPanel';
import DetailTable from './components/DetailTable';

export default {
  name: "YqKeySignal",
  components: { GroupPanel, DetailTable },
  data() {
    return {
      masterGroupList: [],// 接收 GroupPanel 传递过来的完整分组列,并将其传递给 DetailTable。
      activeGroup: {
        id: null, // null 表示全部
        name: '全部信源'
      }
    };
  },
  methods: {
    /**
      * 新方法：接收 GroupPanel 传来的完整分组列表，并存储起来
      * 这个列表将传递给 DetailTable，用于其内部的“所属分组”下拉菜单
      * @param {Array} groupList - 来自 GroupPanel 的完整分组列表
      */
    updateMasterGroupList(groupList) {
      this.masterGroupList = groupList;
    },
    /** 处理信源明细变化事件（增、删、改) */
    handleDetailsChanged() {
       // 父组件的这个事件监听器现在只需要知道“有事发生”即可，无需再重复调用子组件的方法。
    },
    /** 处理分组选择事件 */
    handleGroupSelected(group) {
      this.activeGroup = group;
    },
  }
};
</script>

<style lang="scss" scoped>
.key-signal-container {
  // 如果需要，可以添加一些特定样式
}
</style>
```

## File: src/views/yqkeysignal/components/DetailTable.vue
```vue
<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>{{ activeGroup.name || '全部信源' }}</span>
            </div>

            <!-- 搜索和操作区域 -->
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
                <el-form-item label="账号昵称" prop="accountNickname">
                    <el-input v-model="queryParams.accountNickname" placeholder="请输入账号昵称" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="用户ID" prop="userId">
                    <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="平台用户ID" prop="platformUserId">
                    <el-input v-model="queryParams.platformUserId" placeholder="请输入平台用户ID" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>

                <el-form-item label="主页链接" prop="homepageLink">
                    <el-input v-model="queryParams.homepageLink" placeholder="请输入主页链接" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <!-- 修改“级别分类”的 el-select，添加 multiple -->
                <el-form-item label="级别分类" prop="sourceImportance">
                    <el-select v-model="queryParams.sourceImportance" multiple placeholder="全部级别" clearable
                        style="width: 150px">
                        <el-option v-for="dict in dict.type.source_importance" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>

                <!-- 修改“网民类型”的 el-select，添加 multiple -->
                <el-form-item label="网民类型" prop="audienceTypes">
                    <el-select v-model="queryParams.audienceTypes" multiple placeholder="全部类型" clearable
                        style="width: 150px">
                        <el-option v-for="dict in dict.type.audience_type" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>

                <!-- 修改“来源类别”的 el-select，添加 multiple -->
                <el-form-item label="来源类别" prop="sourceCategories">
                    <el-select v-model="queryParams.sourceCategories" multiple placeholder="全部类别" clearable
                        style="width: 150px">
                        <el-option label="账号" value="1"></el-option>
                        <el-option label="站点" value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="添加时间">
                    <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini"
                        @click="handleAdd">从信源库添加</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                        v-hasPermi="['yq:keysignal:export']">导出数据</el-button>
                </el-col>
                <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <!-- 数据表格 -->
            <el-table v-loading="loading" :data="detailList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column label="账号昵称" align="left" prop="accountNickname" width="180">
                    <template slot-scope="scope">
                        <div>{{ scope.row.accountNickname }}</div>
                        <div class="sub-text">{{ scope.row.remark }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="用户ID" align="left" prop="userId" width="150" :show-overflow-tooltip="true" />
                <el-table-column label="平台" align="center" prop="platform" width="100">
                    <template slot-scope="scope">
                        <el-tag size="small">{{ scope.row.platformName || scope.row.platform }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="二级平台" align="center" prop="secondPlatform" width="100">
                    <template slot-scope="scope">
                        <el-tag type="info" size="small" v-if="scope.row.secondPlatform">{{ scope.row.secondPlatform }}</el-tag>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="主页链接" align="center" width="100">
                    <template slot-scope="scope">
                        <a :href="scope.row.homepageLink" target="_blank" class="button-link">查看链接</a>
                    </template>
                </el-table-column>
                <el-table-column label="所属分组" align="left" prop="groups" width="200">
                    <template slot-scope="scope">
                        <el-tag v-for="group in scope.row.groups" :key="group.id"
                            :style="{ 'background-color': group.groupColor + '20', 'border-color': group.groupColor + '80', color: group.groupColor }"
                            size="small" style="margin-right: 5px; margin-bottom: 5px">
                            {{ group.groupName }}
                        </el-tag>
                        <span v-if="!scope.row.groups || scope.row.groups.length === 0">--</span>
                    </template>
                </el-table-column>
                <el-table-column label="网民类型" align="left" prop="audienceTypes" width="180">
                    <template slot-scope="scope">
                        <el-tag v-for="type in scope.row.audienceTypeNames" :key="type" type="info" size="small"
                            style="margin-right: 5px; margin-bottom: 5px">
                            {{ type }}
                        </el-tag>
                        <span v-if="!scope.row.audienceTypeNames || scope.row.audienceTypeNames.length === 0">--</span>
                    </template>
                </el-table-column>
                <el-table-column label="信源类别" align="center" prop="sourceCategory" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.sourceCategory === '1' ? 'success' : 'warning'" size="small" v-if="scope.row.sourceCategoryName">
                            {{ scope.row.sourceCategoryName }}
                        </el-tag>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="级别分类" align="center" prop="sourceImportance" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="getImportanceTagType(scope.row.sourceImportance)" size="small"
                            v-if="scope.row.sourceImportanceName">{{ scope.row.sourceImportanceName }}</el-tag>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="添加时间" align="center" prop="createTime" width="160" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                    width="120">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" icon="el-icon-edit"
                            @click="handleUpdate(scope.row)">编辑</el-button>
                        <el-button size="mini" type="text" icon="el-icon-delete"
                            @click="handleDelete(scope.row)">移出</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>

        <!-- 添加或修改信源分类对话框 -->
        <DetailForm ref="detailForm" @submit-success="handleRefresh" />
        <!-- 从信源库添加对话框 -->
        <AddFromLibraryDialog ref="addFromLibrary" @submit-success="handleRefresh" />
    </div>
</template>

<script>
import { listDetail, delDetail, exportDetail } from "@/api/yqkeysignal/detail";
import DetailForm from './DetailForm';
import AddFromLibraryDialog from './AddFromLibraryDialog';
import { getDicts } from "@/api/system/dict/data";

export default {
    name: "DetailTable",
    dicts: ['audience_type', 'source_importance', 'content_platform'],
    components: { DetailForm, AddFromLibraryDialog },
    props: {
        activeGroup: {
            type: Object,
            required: true
        },
        // 3. 添加 groupList prop
        groupList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            loading: false,
            showSearch: true,
            ids: [],
            single: true,
            multiple: true,
            total: 0,
            detailList: [],
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                accountNickname: undefined,
                userId: undefined,
                platformUserId: undefined,
                homepageLink: undefined,
                groupIds: [],
                ungrouped: false,
                sourceImportance: [],
                audienceTypes: [],
                sourceCategories: []
            },
            dateRange: [],
            isMounted: false
        }
    },
    mounted() {
        this.isMounted = true;
    },
    watch: {
        activeGroup: {
            handler(newGroup) {
                this.queryParams.pageNum = 1;
                this.queryParams.accountNickname = undefined;
                this.queryParams.userId = undefined;
                this.queryParams.platformUserId = undefined;
                this.queryParams.homepageLink = undefined;
                this.queryParams.sourceCategories = undefined;
                this.dateRange = [];

                if (this.isMounted) {
                    this.$refs.queryForm.resetFields();
                }

                if (newGroup.id === 0) {
                    this.queryParams.ungrouped = true;
                    this.queryParams.groupIds = [];
                } else if (newGroup.id === null) {
                    this.queryParams.ungrouped = false;
                    this.queryParams.groupIds = [];
                } else {
                    this.queryParams.ungrouped = false;
                    this.queryParams.groupIds = [newGroup.id];
                }

                this.getList();
            },
            immediate: true
        },
        // 当分组列表变化时，重新为当前明细数据构建友好的分组展示结构
        groupList: {
            handler() {
                if (!this.detailList || this.detailList.length === 0) return;
                this.detailList = this.detailList.map(item => {
                    item.groups = this.buildGroupsForItem(item);
                    return item;
                });
            }
        }
    },
    methods: {
        // 将后端的 groupIds/groupIdsStr 映射为用于展示的 groups 数组
        buildGroupsForItem(item) {
            const defaultColor = '#409EFF';
            // 1) 解析分组ID列表
            let ids = [];
            if (Array.isArray(item.groupIds)) {
                ids = item.groupIds;
            } else if (typeof item.groupIdsStr === 'string' && item.groupIdsStr.trim() !== '') {
                ids = item.groupIdsStr.split(',').map(v => Number(v)).filter(v => !Number.isNaN(v));
            }

            // 2) 解析同位置的名称（可选，用作兜底）
            let namesByIndex = [];
            if (Array.isArray(item.groupNames)) {
                namesByIndex = item.groupNames;
            } else if (typeof item.groupNamesStr === 'string') {
                namesByIndex = item.groupNamesStr.split(',');
            }

            // 3) 基于 master groupList 做映射，拿到颜色与名称
            const mapped = ids.map((id, index) => {
                const found = this.groupList.find(g => g.groupId === id);
                if (found) {
                    return {
                        id: found.groupId,
                        groupName: found.groupName,
                        groupColor: found.groupColor || defaultColor
                    };
                }
                // 兜底：仅有名称时，也展示标签（使用默认颜色）
                const fallbackName = namesByIndex[index] || String(id);
                return {
                    id,
                    groupName: fallbackName,
                    groupColor: defaultColor
                };
            });

            return mapped;
        },
        mapData(data) {
            return data.map(item => {
                const audienceTypeIds = item.audienceTypes ? String(item.audienceTypes).split(',') : [];
                item.audienceTypeNames = audienceTypeIds.map(id => {
                    const dict = this.dict.type.audience_type.find(d => d.value === id);
                    return dict ? dict.label : '';
                }).filter(Boolean);

                const importanceDict = this.dict.type.source_importance.find(d => d.value === item.sourceImportance);
                item.sourceImportanceName = importanceDict ? importanceDict.label : '';

                // 平台名称：若后端 platform 是编码，尝试用 content_platform 字典映射
                const platformDict = this.dict.type.content_platform && this.dict.type.content_platform.find(d => d.value === item.platform);
                item.platformName = platformDict ? platformDict.label : (item.secondPlatform || item.platform);

                // 信源类别中文名
                if (item.sourceCategory === '1') {
                    item.sourceCategoryName = '账号';
                } else if (item.sourceCategory === '2') {
                    item.sourceCategoryName = '站点';
                } else {
                    item.sourceCategoryName = '';
                }

                // 根据当前分组列表，构建友好的分组展示结构
                item.groups = this.buildGroupsForItem(item);

                return item;
            })
        },
        getList() {
            this.loading = true;
            const params = { ...this.queryParams };
            if (this.dateRange && this.dateRange.length) {
                params.addTimeStart = this.dateRange[0] + " 00:00:00";
                params.addTimeEnd = this.dateRange[1] + " 23:59:59";
            } else {
                params.addTimeStart = undefined;
                params.addTimeEnd = undefined;
            }
            listDetail(params).then(response => {
                this.detailList = this.mapData(response.rows);
                this.total = response.total;
                this.loading = false;
                
                // 打印 detailList 用于调试
                console.log('DetailTable detailList:', this.detailList);
            })
        },
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        resetQuery() {
            this.dateRange = [];
            this.resetForm("queryForm");
            this.handleQuery();
        },
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.detailId);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        handleAdd() {
            this.$refs.addFromLibrary.open(this.groupList, this.activeGroup);
        },
        handleUpdate(row) {
            this.$refs.detailForm.open(row, this.groupList);
        },
        handleDelete(row) {
            const detailIds = row.detailId || this.ids;
            this.$modal.confirm(`是否确认移出账号昵称为【${row.accountNickname}】的信源？`).then(() => {
                return delDetail(detailIds);
            }).then(() => {
                this.handleRefresh();
                this.$modal.msgSuccess("移出成功");
            }).catch(() => { });
        },
        handleExport() {
            // [关键] 预先定义好 application/json 的请求配置
            const jsonPostOptions = {
                headers: { 'Content-Type': 'application/json' },
                // transformRequest 会在请求发送前处理数据，此处是将JS对象序列化为JSON字符串
                transformRequest: [(data) => JSON.stringify(data)]
            };

            // 判断是否有选中的行
            if (this.ids && this.ids.length > 0) {
                // --- 情况一：用户勾选了行，只导出选中项 ---

                // 准备只包含选中ID的参数
                const exportParams = { ids: this.ids };

                this.$modal.confirm(`是否确认导出您选中的 ${this.ids.length} 条数据？`).then(() => {
                    // [修复] 调用 download 时，传入 jsonPostOptions
                    this.download('keySourceDetail/export', exportParams, `重点信源(选中)_${new Date().getTime()}.xlsx`, jsonPostOptions);
                }).catch(() => { });

            } else {
                // --- 情况二：用户未勾选任何行，按原逻辑导出全部筛选结果 ---

                // 准备包含所有搜索条件的参数
                const exportParams = { ...this.queryParams };
                if (this.dateRange && this.dateRange.length) {
                    exportParams.addTimeStart = this.dateRange[0] + " 00:00:00";
                    exportParams.addTimeEnd = this.dateRange[1] + " 23:59:59";
                }

                this.$modal.confirm('您未选择任何数据，将导出所有符合当前筛选条件的数据。是否继续？').then(() => {
                    // [修复] 调用 download 时，也传入 jsonPostOptions
                    this.download('keySourceDetail/export', exportParams, `重点信源(全部)_${new Date().getTime()}.xlsx`, jsonPostOptions);
                }).catch(() => { });
            }
        },
        handleRefresh() {
            this.getList();
            this.$emit('details-changed');
        },
        getImportanceTagType(level) {
            if (level === '1') return 'danger';
            if (level === '2') return 'warning';
            return 'info';
        }
    }
}
</script>
<style scoped>
.sub-text {
    font-size: 12px;
    color: #999;
}

.button-link {
    color: #409EFF;
    text-decoration: none;
}
</style>
```

## File: src/views/yqkeysignal/components/AddFromLibraryDialog.vue
```vue
<template>
  <div>
    <!-- 主对话框：搜索和选择信源 -->
    <el-dialog title="从信源库添加" :visible.sync="mainDialogVisible" width="70%" top="5vh" :close-on-click-modal="false"
      @close="handleMainDialogClose" append-to-body>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="平台筛选" prop="platform">
          <el-select v-model="queryParams.platform" placeholder="全部平台" clearable style="width: 150px">
            <el-option v-for="dict in dict.type.content_platform" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="用户ID" prop="user_id">
          <el-input v-model="queryParams.user_id" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜 索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">清空条件</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-check" size="mini" :disabled="selectedSources.length === 0"
            @click="handleOpenClassificationDialog">批量添加选中项</el-button>
        </el-col>
        <el-col>
          <el-alert v-if="selectedSources.length > 0" :title="`已选择 ${selectedSources.length} 项，共找到 ${total} 条结果`"
            type="info" show-icon :closable="false">
          </el-alert>
        </el-col>
      </el-row>

      <!-- 结果表格 -->
      <el-table v-loading="loading" :data="sourceList" @selection-change="handleSelectionChange" ref="searchTable">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="账号昵称" prop="nickname" :show-overflow-tooltip="true" />
        <el-table-column label="用户ID" prop="user_id" :show-overflow-tooltip="true" />
        <el-table-column label="平台用户ID" prop="short_id" :show-overflow-tooltip="true" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.short_id || '--' }}</span>
          </template> 
        </el-table-column>
        <el-table-column label="平台" prop="platform" align="center" width="100" />
        <el-table-column label="二级平台" prop="platform_name" align="center" width="100" />
        <el-table-column label="主页链接" align="center">
          <template slot-scope="scope">
            <a :href="scope.main_domain" target="_blank" class="button-link">查看链接</a>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button v-if="scope.row.is_added !== 1" size="mini" type="text" @click="handleSingleAdd(scope.row)">
              <span v-if="['weibo', 'weixin', 'douyin', 'toutiao', 'xiaohongshu'].includes(scope.row.platform)">
                添加账号
              </span>
              <span v-else>添加站点</span>
            </el-button>
            <span v-else style="color: #c0c4cc; font-size: 12px;">已添加</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.page_size"
        @pagination="getList" />
    </el-dialog>

    <!-- 子对话框：配置分类信息 -->
    <el-dialog title="信源分类信息" :visible.sync="classificationDialogVisible" width="600px" append-to-body>

      <el-alert :title="`将为选中的 ${selectedSources.length} 个项目应用这些配置`" type="info" show-icon :closable="false"
        style="margin-bottom: 20px;"></el-alert>

      <el-form ref="classificationForm" :model="classificationForm" label-width="100px">
        <el-form-item label="级别分类" prop="sourceImportance">
          <el-select v-model="classificationForm.sourceImportance" placeholder="请选择级别分类" style="width: 100%;">
            <el-option v-for="dict in dict.type.source_importance" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="网民类型" prop="audienceTypes">
          <el-select v-model="classificationForm.audienceTypes" multiple placeholder="请选择网民类型 (可多选)"
            style="width: 100%;">
            <el-option v-for="dict in dict.type.audience_type" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属分组" prop="groupIds">
          <el-select v-model="classificationForm.groupIds" multiple placeholder="请选择所属分组 (可多选)" style="width: 100%;">
            <el-option v-for="group in groupList" :key="group.groupId" :label="group.groupName" :value="group.groupId">
              <span style="float: left">{{ group.groupName }}</span>
              <span class="option-color-dot" :style="{ backgroundColor: group.groupColor }"></span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="classificationDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitBatchAdd" :loading="submitLoading">确 认 添 加</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { searchUserFromUniversal } from "@/api/yqkeysignal/universal";
import { batchAddDetail } from "@/api/yqkeysignal/detail";

export default {
  name: "AddFromLibraryDialog",
  dicts: ['content_platform', 'audience_type', 'source_importance'],
  data() {
    return {
      // 主对话框
      mainDialogVisible: false,
      loading: false,
      sourceList: [],
      total: 0,
      queryParams: {
        page: 1,
        page_size: 10,
        platform: undefined,
        keyword: undefined,
        user_id: undefined,
        main_domain: undefined,
        direct_id: null 
      },
      selectedSources: [],

      // 分类对话框
      classificationDialogVisible: false,
      submitLoading: false,
      classificationForm: {
        sourceImportance: undefined,
        audienceTypes: [],
        groupIds: [],
      },

      // 共享数据
      groupList: [],
    };
  },
  methods: {
    // ---- 对外暴露方法 ----
    open(groupList,activeGroup) {
      this.groupList = groupList;
      this.mainDialogVisible = true;
      this.queryParams.direct_id = activeGroup ? activeGroup.id : null;
    },

    // ---- 搜索与列表逻辑 ----
    getList() {
      this.loading = true;
      searchUserFromUniversal(this.queryParams).then(response => {
        this.sourceList = response.data.list || [];
        this.total = response.data.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    resetQuery() {
      const directId = this.queryParams.direct_id;
      this.resetForm("queryForm");
      this.queryParams.direct_id = directId;
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.selectedSources = selection;
    },
    handleMainDialogClose() {
      this.selectedSources = [];
      if (this.$refs.searchTable) {
        this.$refs.searchTable.clearSelection();
      }
    },

    // ---- 添加逻辑 ----
    handleSingleAdd(row) {
      this.selectedSources = [row];
      this.handleOpenClassificationDialog();
    },
    handleOpenClassificationDialog() {
      if (this.selectedSources.length === 0) {
        this.$modal.msgWarning("请至少选择一项要添加的信源");
        return;
      }
      // 重置分类表单
      this.classificationForm = {
        sourceImportance: undefined,
        audienceTypes: [],
        groupIds: [],
      };
      this.classificationDialogVisible = true;
    },
    submitBatchAdd() {
      this.submitLoading = true;
      const details = this.selectedSources.map(item => {
        return {
          userId: item.user_id,
          accountNickname: item.nickname,
          platformUserId: item.short_id || '',
          homepageLink: item.main_domain,
          platform: item.platform,
          secondPlatform: item.platform_name,
          remark: item.remark || "",
          sourceCategory: (['weibo', 'weixin', 'douyin', 'toutiao', 'xiaohongshu'].includes(item.platform)) ? '1' : '2' // 1-账号, 2-站点
        }
      });
      const classificationInfo = {
        groupIds: this.classificationForm.groupIds,
        audienceTypes: this.classificationForm.audienceTypes.join(','),
        sourceImportance: this.classificationForm.sourceImportance
      };

      const payload = {
        details: details,
        classificationInfo: classificationInfo
      };

      batchAddDetail(payload).then(() => {
        this.$modal.msgSuccess(`成功添加 ${details.length} 个信源`);
        this.submitLoading = false;
        this.classificationDialogVisible = false;
        this.mainDialogVisible = false;
        this.$emit("submit-success");
      }).catch(() => {
        this.submitLoading = false;
      })
    }
  }
};
</script>

<style scoped>
.button-link {
  color: #409EFF;
  text-decoration: none;
}

.option-color-dot {
  float: right;
  margin-top: 13px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
</style>
```
